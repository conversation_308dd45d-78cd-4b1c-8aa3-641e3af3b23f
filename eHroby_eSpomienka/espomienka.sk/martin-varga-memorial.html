<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Digitálna spomienka na Martina Vargu - ho<PERSON><PERSON><PERSON> vodcu, fotografa prírody a hudobníka z Liptova">
    <title>Spomienka na Martina Vargu | 1975 - 2023</title>

    <!-- Permissions Policy pre YouTube -->
    <meta http-equiv="Permissions-Policy" content="autoplay=*, encrypted-media=*, accelerometer=*, gyroscope=*, picture-in-picture=*, clipboard-write=*, web-share=*">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Lora:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Mapbox GL JS -->
    <script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>
    <link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />

    <!-- YouTube Player API -->
    <script src="https://www.youtube.com/iframe_api"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'lora': ['Lora', 'serif'],
                    },
                    colors: {
                        'mountain': {
                            50: '#f7f8f7',
                            100: '#eef0ed',
                            200: '#dde1da',
                            300: '#c4cbbf',
                            400: '#a8b2a1',
                            500: '#8b9584',
                            600: '#7B8471',
                            700: '#4A6741',
                            800: '#3d5436',
                            900: '#32452c',
                        },
                        'earth': {
                            50: '#f9f7f4',
                            100: '#f2ede6',
                            200: '#e4d9cc',
                            300: '#d2c0a8',
                            400: '#bfa382',
                            500: '#a68a64',
                            600: '#8B7355',
                            700: '#735f47',
                            800: '#5f4f3d',
                            900: '#4f4235',
                        },
                        'stone': {
                            50: '#fafafa',
                            100: '#f4f4f5',
                            200: '#e4e4e7',
                            300: '#d4d4d8',
                            400: '#a1a1aa',
                            500: '#71717a',
                            600: '#52525b',
                            700: '#3f3f46',
                            800: '#27272a',
                            900: '#18181b',
                        }
                    }
                }
            }
        }
    </script>

    <style>
    
    <style>
        .hero-bg {
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.3)),
                        url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
            overflow: hidden;
        }

        /* Paralax efekt pre hero pozadie */
        .hero-parallax {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 120%;
            background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.3)),
                        url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
            background-size: cover;
            background-position: center;
            will-change: transform;
            z-index: -1;
        }

        /* Particles animácia */
        .particles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        /* Progress bar */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #4A6741, #7B8471);
            z-index: 1000;
            transition: width 0.1s ease;
        }

        /* Smooth scroll */
        html {
            scroll-behavior: smooth;
        }

        /* Navigation štýly */
        .nav-link {
            position: relative;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #4A6741;
        }

        .nav-link.active {
            color: #4A6741;
            font-weight: 600;
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #4A6741;
        }

        /* Mobile menu */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 4px;
        }

        .hamburger span {
            width: 25px;
            height: 3px;
            background: #4A6741;
            margin: 3px 0;
            transition: 0.3s;
        }

        .mobile-menu {
            display: none;
            position: fixed;
            top: 70px;
            left: 0;
            width: 100%;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            flex-direction: column;
            padding: 20px;
        }

        .mobile-menu a {
            padding: 15px 0;
            text-decoration: none;
            color: #52525b;
            border-bottom: 1px solid #e4e4e7;
            transition: color 0.3s ease;
        }

        .mobile-menu a:hover,
        .mobile-menu a.active {
            color: #4A6741;
        }

        @media (max-width: 768px) {
            .hamburger {
                display: flex;
            }

            nav .hidden.md\\:flex {
                display: none !important;
            }

            .mobile-menu {
                display: flex;
            }
        }
        
        /* CSS premenné pre galériu */
        :root {
            --brand-mountain: #4A6741;
            --text-light: #6b7280;
            --bg-main: #fdfdfc;
        }

        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .gallery-item {
            transition: transform 0.3s ease;
        }
        
        .gallery-item:hover {
            transform: scale(1.05);
        }
        
        /* Audio prehrávač CSS */
        .audio-player {
            background: linear-gradient(135deg, #f7f8f7, #eef0ed);
            border: 2px solid #dde1da;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .track-info {
            text-align: center;
            margin-bottom: 20px;
        }

        .track-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #4A6741;
            margin-bottom: 5px;
        }

        .track-artist {
            color: #7B8471;
            font-size: 1rem;
        }

        .audio-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }

        .audio-btn {
            background: #4A6741;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .audio-btn:hover {
            background: #3d5436;
            transform: scale(1.1);
        }

        .play-pause {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .progress-container {
            width: 100%;
            height: 6px;
            background: #dde1da;
            border-radius: 3px;
            margin: 15px 0;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4A6741, #7B8471);
            border-radius: 3px;
            width: 0%;
            transition: width 0.1s ease;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #7B8471;
            margin-top: 10px;
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
        }

        .volume-slider {
            flex: 1;
            height: 4px;
            background: #dde1da;
            border-radius: 2px;
            outline: none;
            cursor: pointer;
        }

        .volume-slider::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            background: #4A6741;
            border-radius: 50%;
            cursor: pointer;
        }

        .playlist {
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
        }

        .playlist-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .playlist-item:hover {
            background: rgba(74, 103, 65, 0.1);
        }

        .playlist-item.active {
            background: rgba(74, 103, 65, 0.2);
            border-left: 4px solid #4A6741;
        }

        .playlist-item .track-number {
            width: 30px;
            text-align: center;
            color: #7B8471;
            font-weight: 600;
        }

        .playlist-item .track-details {
            flex: 1;
            margin-left: 10px;
        }

        .playlist-item .track-name {
            font-weight: 500;
            color: #4A6741;
        }

        .playlist-item .track-artist {
            font-size: 0.85rem;
            color: #7B8471;
            font-style: italic;
        }

        .playlist-item .track-duration {
            font-size: 0.9rem;
            color: #7B8471;
        }
        /* Mapbox štýly */
        .map-container {
            height: 400px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .map-placeholder {
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed #4A6741;
            position: relative;
            overflow: hidden;
        }

        .map-placeholder::before {
            content: '🏔️';
            font-size: 4rem;
            opacity: 0.3;
            position: absolute;
            top: 20px;
            left: 20px;
        }

        .map-placeholder::after {
            content: '🌲';
            font-size: 3rem;
            opacity: 0.3;
            position: absolute;
            bottom: 20px;
            right: 20px;
        }

        .map-info {
            text-align: center;
            color: #666;
        }

        .map-points {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .map-point {
            background: linear-gradient(135deg, rgba(123, 132, 113, 0.1), rgba(139, 115, 85, 0.1));
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4A6741;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .map-point:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(74, 103, 65, 0.2);
        }

        .map-point-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #4A6741;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .map-point-title::before {
            content: '📍';
            margin-right: 8px;
        }

        /* Quotes carousel */
        .quotes-carousel {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .quote-slide {
            display: none;
            text-align: center;
            animation: fadeInQuote 0.8s ease-in-out;
        }

        .quote-slide.active {
            display: block;
        }

        .quote-text {
            font-size: 1.5rem;
            font-style: italic;
            color: #4A6741;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .quote-author {
            font-size: 1rem;
            color: #7B8471;
            font-weight: 600;
        }

        .carousel-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 30px;
        }

        .carousel-btn {
            background: #4A6741;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .carousel-btn:hover {
            background: #3d5436;
            transform: scale(1.1);
        }

        .carousel-dots {
            display: flex;
            gap: 8px;
        }

        .carousel-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #dde1da;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .carousel-dot.active {
            background: #4A6741;
        }

        @keyframes fadeInQuote {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Section dividers */
        .section-divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #dde1da, transparent);
            margin: 3rem 0;
        }

        /* Timeline štýly s horskou paletou */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .header h2 {
            font-size: 3rem;
            font-weight: 600;
            color: #4A6741;
            margin-bottom: 15px;
            font-family: 'Lora', serif;
        }

        .header p {
            font-size: 1.2rem;
            color: #7B8471;
            font-style: italic;
        }

        .timeline {
            position: relative;
            max-width: 900px;
            margin: 0 auto;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #4A6741, #7B8471);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin: 40px 0;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .timeline-item.animate {
            opacity: 1;
            transform: translateY(0);
        }

        .timeline-item:nth-child(odd) .timeline-content {
            left: 0;
            text-align: right;
            padding-right: 40px;
        }

        .timeline-item:nth-child(even) .timeline-content {
            left: 50%;
            text-align: left;
            padding-left: 40px;
        }

        .timeline-content {
            position: relative;
            width: 50%;
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .content-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            float: left;
            margin-right: 20px;
            margin-bottom: 10px;
            border: 3px solid #4A6741;
            position: relative;
            overflow: hidden;
            transition: opacity 0.3s ease;
        }

        .content-photo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(74, 103, 65, 0.3), rgba(123, 132, 113, 0.3));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .timeline-content:hover .content-photo::before {
            opacity: 1;
        }

        .photo-overlay {
            position: absolute;
            bottom: -30px;
            left: 0;
            right: 0;
            background: rgba(74, 103, 65, 0.9);
            color: white;
            padding: 5px;
            font-size: 0.8rem;
            text-align: center;
            transform: translateY(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .timeline-content:hover .photo-overlay {
            opacity: 1;
            transform: translateY(0);
        }

        .timeline-dot {
            position: absolute;
            left: 50%;
            top: 50%;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #4A6741, #7B8471);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            box-shadow: 0 0 20px rgba(74, 103, 65, 0.5);
        }

        .timeline-dot::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }

        .year {
            font-size: 1.8rem;
            font-weight: bold;
            color: #4A6741;
            margin-bottom: 10px;
        }

        .phase {
            font-size: 1.3rem;
            color: #7B8471;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .description {
            color: #666;
            line-height: 1.6;
        }

        /* Memorial section */
        .memorial {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .memorial h2 {
            color: #4A6741;
            margin-bottom: 15px;
            font-size: 2rem;
        }

        .heart {
            color: #8B7355;
            animation: heartbeat 2s ease-in-out infinite;
        }

        @keyframes heartbeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .memorial-gallery {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .memorial-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            border: 3px solid #4A6741;
            transition: transform 0.3s ease;
        }

        .memorial-photo:hover {
            transform: scale(1.1);
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .timeline::before {
                left: 30px;
            }

            .timeline-item .timeline-content {
                width: calc(100% - 80px);
                left: 60px !important;
                text-align: left !important;
                padding-left: 20px !important;
                padding-right: 20px !important;
            }

            .timeline-dot {
                left: 30px;
            }

            .content-photo {
                width: 60px;
                height: 60px;
                margin-right: 15px;
            }

            .year {
                font-size: 1.5rem;
            }

            .phase {
                font-size: 1.1rem;
            }
        }

        /* Accessibility */
        .focus-visible:focus {
            outline: 2px solid #4A6741;
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .timeline-content {
                border: 2px solid #000;
            }

            .timeline-dot {
                border: 2px solid #000;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .timeline-item {
                transition: none;
            }

            .particle {
                animation: none;
            }

            .heart {
                animation: none;
            }
        }

        /* Skip link for accessibility */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: #4A6741;
            color: white;
            padding: 8px;
            text-decoration: none;
            z-index: 1000;
            border-radius: 4px;
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Profilová fotka štýly */
        .profile-photo-container {
            position: relative;
            transition: transform 0.3s ease;
        }

        .profile-photo-container:hover {
            transform: scale(1.05);
        }

        .profile-photo-container::before {
            content: '';
            position: absolute;
            inset: -4px;
            background: linear-gradient(45deg, #4A6741, #7B8471, #8B7355);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .profile-photo-container:hover::before {
            opacity: 1;
        }
    </style>
</head>

<body class="bg-stone-50 font-inter">
    <!-- Progress Bar -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link focus-visible">Preskočiť na hlavný obsah</a>

    <!-- Sticky Navigation -->
    <nav class="fixed top-0 w-full bg-white/90 backdrop-blur-sm shadow-sm z-50" role="navigation" aria-label="Hlavná navigácia">
        <div class="max-w-6xl mx-auto px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-lora font-semibold text-stone-800">Spomienka na Martina Vargu</h1>
                <div class="hidden md:flex space-x-8" role="menubar">
                    <a href="#zivot" class="nav-link text-stone-600 hover:text-mountain-700 transition-colors focus-visible" role="menuitem">Život</a>
                    <a href="#timeline" class="nav-link text-stone-600 hover:text-mountain-700 transition-colors focus-visible" role="menuitem">Životná cesta</a>
                    <a href="#galeria" class="nav-link text-stone-600 hover:text-mountain-700 transition-colors focus-visible" role="menuitem">Galéria</a>
                    <a href="#hudba" class="nav-link text-stone-600 hover:text-mountain-700 transition-colors focus-visible" role="menuitem">Hudba</a>
                    <a href="#spomienky" class="nav-link text-stone-600 hover:text-mountain-700 transition-colors focus-visible" role="menuitem">Spomienky</a>
                    <a href="#mapa" class="nav-link text-stone-600 hover:text-mountain-700 transition-colors focus-visible" role="menuitem">Mapa</a>
                </div>
                <div class="hamburger md:hidden" id="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobileMenu">
        <a href="#zivot" class="mobile-nav-link">Život</a>
        <a href="#timeline" class="mobile-nav-link">Životná cesta</a>
        <a href="#galeria" class="mobile-nav-link">Galéria</a>
        <a href="#hudba" class="mobile-nav-link">Hudba</a>
        <a href="#spomienky" class="mobile-nav-link">Spomienky</a>
        <a href="#mapa" class="mobile-nav-link">Mapa</a>
    </div>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Hero Section -->
        <section class="hero-bg min-h-screen flex items-center justify-center text-white" aria-label="Úvodná sekcia">
        <div class="hero-parallax"></div>
        <div class="particles-container" id="particles"></div>

        <div class="text-center px-6 max-w-4xl mx-auto relative z-10">
            <h1 class="text-6xl md:text-8xl font-lora font-light mb-4 fade-in">Martin Varga</h1>
            <p class="text-2xl md:text-3xl font-light mb-8 fade-in">1975 – 2023</p>
            <div class="max-w-3xl mx-auto fade-in">
                <p class="text-lg md:text-xl leading-relaxed">
                    Človek, ktorý našiel svoj pokoj v tichu hôr a ktorého odkaz žije v každom štíte,
                    ktorý zdolal, v každej piesni, ktorú zložil, a v srdciach tých, ktorých sa dotkol.
                </p>
            </div>
        </div>
        </section>

        <!-- Profilová fotka Section -->
        <section class="py-16 bg-stone-50">
            <div class="max-w-4xl mx-auto px-6 text-center">
                <div class="fade-in">
                    <div class="w-64 h-64 mx-auto mb-8 profile-photo-container">
                        <img src="./assets/images/profil.png"
                             alt="Martin Varga - profilová fotka"
                             class="w-full h-full object-cover rounded-full border-8 border-mountain-700 shadow-2xl">
                        <div class="absolute inset-0 rounded-full bg-gradient-to-t from-mountain-700/20 to-transparent"></div>
                    </div>
                    <h2 class="text-3xl font-lora font-semibold text-mountain-700 mb-4">Martin Varga</h2>
                    <p class="text-xl text-stone-600 font-lora italic mb-6">Horský vodca • Hudobník • Otec • Priateľ</p>
                    <p class="text-lg text-stone-600 max-w-2xl mx-auto leading-relaxed">
                        Muž, ktorý žil svoj život naplno - s láskou k horám, vášňou pre hudbu a nekonečnou oddanosťou svojej rodine.
                        Jeho odkaz žije v každom vrchole, ktorý zdolal, v každej melódii, ktorú vytvoril, a v srdciach všetkých, ktorých sa dotkol.
                    </p>
                </div>
            </div>
        </section>

        <!-- Život Section -->
        <section id="zivot" class="py-20 bg-white">
            <div class="max-w-4xl mx-auto px-6">
                <div class="text-center mb-16 fade-in">
                    <h2 class="text-4xl lg:text-5xl font-lora font-semibold text-mountain-700 mb-6">Život v horách</h2>
                    <p class="text-xl text-stone-600 font-lora italic">Príbeh muža, ktorý našiel svoj domov medzi vrcholmi</p>
                </div>

                <div class="grid md:grid-cols-2 gap-12 items-center mb-16">
                    <div class="fade-in">
                        <img src="./assets/images/časozber/mladý_2.png"
                             alt="Martin Varga v mladosti"
                             class="w-32 h-32 rounded-full object-cover border-4 border-mountain-700 shadow-lg mx-auto md:mx-0 mb-6">
                        <div class="bg-stone-100 p-8 rounded-lg shadow-lg">
                            <h3 class="text-2xl font-lora font-semibold text-mountain-700 mb-4">Začiatky v Liptove</h3>
                            <p class="text-stone-600 leading-relaxed mb-4">
                                Martin sa narodil v roku 1975 v srdci Liptova, kde sa už od detstva zamiloval do majestátnych Tatier.
                                Prvé kroky do hôr urobil so svojím otcom, ktorý mu ukázal krásu a pokoru, ktorú si príroda vyžaduje.
                            </p>
                            <p class="text-stone-600 leading-relaxed">
                                Už ako mladý chlapec vedel, že jeho životom budú hory. Každý víkend trávil na chodníkoch,
                                učil sa čítať mapy a poznávať každý štít v okolí.
                            </p>
                        </div>
                    </div>

                    <div class="fade-in">
                        <img src="./assets/images/rodina/vlado_13856_Authentic_candid_photo_of_a_Slovak_man_with_his_f_5b771139-0bfa-4b90-8676-27cd6bc7d3ac_1.png"
                             alt="Martin s rodinou"
                             class="w-32 h-32 rounded-full object-cover border-4 border-mountain-700 shadow-lg mx-auto md:mx-0 mb-6">
                        <div class="bg-stone-100 p-8 rounded-lg shadow-lg">
                            <h3 class="text-2xl font-lora font-semibold text-mountain-700 mb-4">Rodina a láska</h3>
                            <p class="text-stone-600 leading-relaxed mb-4">
                                V roku 2005 stretol svoju životnú lásku Annu pri Štrbskom plese. Bola to láska na prvý pohľad,
                                spojená spoločnou vášňou pre hory a prírodu. O dva roky neskôr sa vzali v malej kaplnke v Tatranskej Lomnici.
                            </p>
                            <p class="text-stone-600 leading-relaxed">
                                Spolu vychovali dve deti - Tomáša a Zuzanu, ktorým odovzdal lásku k horám a úctu k prírode.
                                Rodinné túry boli ich najkrajšími spoločnými chvíľami.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Quotes Carousel -->
                <div class="quotes-carousel fade-in">
                    <div class="quote-slide active">
                        <p class="quote-text">"Hory ma naučili pokore. Každý vrchol je len začiatkom cesty k ďalšiemu."</p>
                        <p class="quote-author">— Martin Varga</p>
                    </div>
                    <div class="quote-slide">
                        <p class="quote-text">"Najkrajšie úsvity som videl s Martinom. Vedel nájsť krásu v každom okamihu."</p>
                        <p class="quote-author">— Anna Vargová</p>
                    </div>
                    <div class="quote-slide">
                        <p class="quote-text">"Otec nás naučil, že skutočné bohatstvo nie je v tom, čo vlastníme, ale v tom, čo zažívame."</p>
                        <p class="quote-author">— Tomáš Varga</p>
                    </div>

                    <div class="carousel-controls">
                        <button class="carousel-btn" id="prevQuote" aria-label="Predchádzajúci citát">‹</button>
                        <div class="carousel-dots">
                            <span class="carousel-dot active" data-slide="0"></span>
                            <span class="carousel-dot" data-slide="1"></span>
                            <span class="carousel-dot" data-slide="2"></span>
                        </div>
                        <button class="carousel-btn" id="nextQuote" aria-label="Ďalší citát">›</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Jemný predel -->
        <div class="section-divider"></div>

        <!-- Timeline Section -->
        <section id="timeline" class="py-20 bg-stone-100">
            <div class="container">
                <!-- Hlavička -->
                <div class="header">
                    <h2>Životná cesta</h2>
                    <p>1975 – 2024 • Životná cesta plná lásky, hudby a hôr</p>
                </div>

                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="content-photo" style="background-image: url('./assets/images/časozber/detstvo.png'); background-position: center -120px;">
                                <div class="photo-overlay">Tatry - miesto, kde sa všetko začalo</div>
                            </div>
                            <div class="year">1975 - 1990</div>
                            <div class="phase">Detstvo v Liptove</div>
                            <div class="description">Prvé kroky do hôr s otcom. Učenie sa lásky k prírode a úcty k horám. Detské sny o dobrodružstvách medzi vrcholmi.</div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="content-photo" style="background-image: url('./assets/images/časozber/mladosť.png'); background-position: center -100px;">
                                <div class="photo-overlay">Prvé akordy pri večernom západe slnka</div>
                            </div>
                            <div class="year">1990 - 2000</div>
                            <div class="phase">Mladosť a hudba</div>
                            <div class="description">Objavenie lásky k hudbe. Prvé skladby inšpirované krásou Tatier. Štúdium na konzervatóriu a prvé koncerty v horských chatách.</div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="content-photo" style="background-image: url('./assets/images/časozber/mladý_2.png');">
                                <div class="photo-overlay">Vysokohorské chodníky boli jeho druhým domovom</div>
                            </div>
                            <div class="year">2000 - 2005</div>
                            <div class="phase">Horský vodca</div>
                            <div class="description">Získanie licencie horského vodcu. Prvé expedície do vysokých Tatier. Zdieľanie lásky k horám s turistami z celého sveta.</div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="content-photo" style="background-image: url('./assets/images/rodina/vlado_13856_Authentic_candid_photo_of_a_Slovak_man_with_his_f_5b771139-0bfa-4b90-8676-27cd6bc7d3ac_1.png');">
                                <div class="photo-overlay">Prvé stretnutie pri horskom jazere</div>
                            </div>
                            <div class="year">2005 - 2007</div>
                            <div class="phase">Láska a svadba</div>
                            <div class="description">Stretnutie s Annou pri Štrbskom plese. Láska na prvý pohľad. Svadba v kaplnke v Tatranskej Lomnici s výhľadom na milované hory.</div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="content-photo" style="background-image: url('./assets/images/časozber/rodina.png'); background-position: center -60px;">
                                <div class="photo-overlay">Rodinné túry boli najkrajšími chvíľami</div>
                            </div>
                            <div class="year">2007 - 2020</div>
                            <div class="phase">Otcovstvo a rodina</div>
                            <div class="description">Narodenie detí Tomáša a Zuzany. Rodinné túry do hôr. Učenie detí láske k prírode a zodpovednosti voči životnému prostrediu.</div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="content-photo" style="background-image: url('./assets/images/rodina/vlado_13856_Candid_photorealistic_photo_of_a_Slovak_man_in_hi_affc4612-143f-4665-999b-36cc4afd7034_2.png');">
                                <div class="photo-overlay">Pokojné chvíle pri písaní spomienok</div>
                            </div>
                            <div class="year">2020 - 2023</div>
                            <div class="phase">Múdrosť a dedičstvo</div>
                            <div class="description">Písanie knihy o horách a živote. Zdieľanie múdrosti s mladými horolezcami. Posledné veľké expedície a príprava odkazu pre budúce generácie.</div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="content-photo" style="background-image: url('./assets/images/časozber/spomienka.png'); background-position: center top;">
                                <div class="photo-overlay">Večný pokoj v objatí hôr</div>
                            </div>
                            <div class="year">2023</div>
                            <div class="phase">Večný odpočinok</div>
                            <div class="description">Martin nás opustil pokojne vo svojom milovanom dome s výhľadom na Tatry. Jeho odkaz žije v každom, koho inšpiroval k láske k horám a prírode.</div>
                        </div>
                    </div>
                </div>

                <div class="memorial">
                    <h2>Spomíname <span class="heart">♥</span></h2>
                    <div class="memorial-gallery">
                        <div class="memorial-photo" style="background-image: url('./assets/images/časozber/detstvo.png');"></div>
                        <div class="memorial-photo" style="background-image: url('./assets/images/časozber/mladosť.png');"></div>
                        <div class="memorial-photo" style="background-image: url('./assets/images/časozber/rodina.png');"></div>
                        <div class="memorial-photo" style="background-image: url('./assets/images/časozber/spomienka.png');"></div>
                    </div>
                    <p>
                        Martin nás naučil, že skutočné bohatstvo sa neskrýva v majetku, ale v láske k blízkym,
                        pokore pred prírodou a radosti z maličkostí. Hory, ktoré miloval, budú navždy niesť jeho tichý odkaz.
                    </p>
                </div>
            </div>
        </section>

        <!-- Jemný predel -->
        <div class="section-divider"></div>

        <!-- Galéria Section -->
        <section id="galeria" class="py-24 bg-white">
            <div class="max-w-7xl mx-auto px-6">
                <div class="text-center mb-20 fade-in">
                    <h2 class="text-4xl lg:text-5xl font-lora font-semibold text-mountain-700 mb-4">Okamihy zachytené v čase</h2>
                    <p class="text-xl text-stone-600 font-lora italic">Spomienky, ktoré zostávajú</p>
                </div>
                <!-- Mriežka pre galériu -->
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                    <!-- Hlavný obrázok, ktorý zaberá viac miesta -->
                    <div class="gallery-item col-span-2 row-span-2 fade-in">
                        <img src="./assets/images/rodina/vlado_13856_Authentic_candid_photo_of_a_Slovak_man_with_his_f_5b771139-0bfa-4b90-8676-27cd6bc7d3ac_1.png"
                             alt="Martin s rodinou v horách"
                             class="w-full h-full object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
                             loading="lazy">
                    </div>

                    <!-- Menšie obrázky -->
                    <div class="gallery-item fade-in">
                        <img src="./assets/images/časozber/detstvo.png"
                             alt="Martin v detstve"
                             class="w-full h-48 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
                             loading="lazy">
                    </div>

                    <div class="gallery-item fade-in">
                        <img src="./assets/images/časozber/mladosť.png"
                             alt="Martin v mladosti"
                             class="w-full h-48 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
                             loading="lazy">
                    </div>

                    <div class="gallery-item fade-in">
                        <img src="./assets/images/časozber/mladý_2.png"
                             alt="Martin ako mladý muž"
                             class="w-full h-48 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
                             loading="lazy">
                    </div>

                    <div class="gallery-item fade-in">
                        <img src="./assets/images/časozber/rodina.png"
                             alt="Martin s rodinou"
                             class="w-full h-48 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
                             loading="lazy">
                    </div>

                    <div class="gallery-item col-span-2 fade-in">
                        <img src="./assets/images/rodina/vlado_13856_Candid_photograph_of_a_Slovak_man_in_his_30s_with_5271ac2f-d23d-4971-8263-354de99b7818_2.png"
                             alt="Martin v prírode"
                             class="w-full h-64 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
                             loading="lazy">
                    </div>

                    <div class="gallery-item fade-in">
                        <img src="./assets/images/časozber/spomienka.png"
                             alt="Spomienka na Martina"
                             class="w-full h-48 object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
                             loading="lazy">
                    </div>
                </div>
            </div>
        </section>

        <!-- Jemný predel -->
        <div class="section-divider"></div>

        <!-- Hudba Section -->
        <section id="hudba" class="py-24 bg-stone-100">
            <div class="max-w-4xl mx-auto px-6">
                <div class="text-center mb-16 fade-in">
                    <h2 class="text-4xl lg:text-5xl font-lora font-semibold text-mountain-700 mb-4">Hudba duše</h2>
                    <p class="text-xl text-stone-600 font-lora italic">Melódie inšpirované krásou Tatier</p>
                </div>

                <div class="audio-player fade-in" id="audioPlayer">
                    <!-- Track Info -->
                    <div class="track-info">
                        <div class="track-title" id="trackTitle">Banská Bystrica</div>
                        <div class="track-artist">Honza Nedved</div>
                    </div>

                    <!-- YouTube Player Container -->
                    <div id="youtube-player-container" style="margin: 20px 0;">
                        <div id="youtube-player"></div>
                    </div>

                    <!-- Custom Controls -->
                    <div class="audio-controls" role="group" aria-label="Ovládanie prehrávania hudby">
                        <button class="audio-btn focus-visible" id="prevBtn" aria-label="Predchádzajúca skladba">⏮</button>
                        <button class="audio-btn play-pause focus-visible" id="playPauseBtn" aria-label="Prehrať/Pozastaviť">▶</button>
                        <button class="audio-btn focus-visible" id="nextBtn" aria-label="Ďalšia skladba">⏭</button>

                        <div class="time-display">
                            <span id="currentTime">0:00</span> / <span id="totalTime">0:00</span>
                        </div>
                    </div>

                    <!-- Volume Control -->
                    <div class="volume-control">
                        <span class="volume-icon">🔊</span>
                        <input type="range" class="volume-slider" id="volumeSlider" min="0" max="100" value="70">
                    </div>

                    <!-- Playlist -->
                    <div class="playlist" id="playlist">
                        <div class="playlist-item active" data-video-id="Ywg2pvva-wg">
                            <div class="track-number">1</div>
                            <div class="track-details">
                                <div class="track-name">Banská Bystrica</div>
                                <div class="track-artist">Honza Nedved</div>
                                <div class="track-duration">4:20</div>
                            </div>
                        </div>
                        <div class="playlist-item" data-video-id="lRca7evReSs">
                            <div class="track-number">2</div>
                            <div class="track-details">
                                <div class="track-name">Tam u nebeských bran</div>
                                <div class="track-artist">Michal Tučný</div>
                                <div class="track-duration">3:45</div>
                            </div>
                        </div>
                        <div class="playlist-item" data-video-id="Epkxt9kmTjE">
                            <div class="track-number">3</div>
                            <div class="track-details">
                                <div class="track-name">Rosa na kolejích</div>
                                <div class="track-artist">Wabi Daněk</div>
                                <div class="track-duration">4:15</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Jemný predel -->
        <div class="section-divider"></div>

        <!-- Spomienky Section -->
        <section id="spomienky" class="py-24 bg-white">
            <div class="max-w-4xl mx-auto px-6">
                <div class="text-center mb-16 fade-in">
                    <h2 class="text-4xl lg:text-5xl font-lora font-semibold text-mountain-700 mb-4">Kondolenčná kniha</h2>
                    <p class="text-xl text-stone-600 font-lora italic">Zdieľajte svoje spomienky na Martina</p>
                </div>

                <!-- Formulár pre pridanie spomienky -->
                <div class="bg-stone-100 p-8 rounded-lg shadow-lg mb-12 fade-in">
                    <form id="memoryForm" class="space-y-6">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="meno" class="block text-sm font-medium text-mountain-700 mb-2">Vaše meno</label>
                                <input type="text" id="meno" name="meno" required
                                       class="w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-mountain-700 focus:border-transparent">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-mountain-700 mb-2">Email (nepovinné)</label>
                                <input type="email" id="email" name="email"
                                       class="w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-mountain-700 focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <label for="spomienka" class="block text-sm font-medium text-mountain-700 mb-2">Vaša spomienka</label>
                            <textarea id="spomienka" name="spomienka" rows="4" required
                                      placeholder="Zdieľajte svoju spomienku na Martina..."
                                      class="w-full px-4 py-3 border border-stone-300 rounded-lg focus:ring-2 focus:ring-mountain-700 focus:border-transparent"></textarea>
                        </div>
                        <div class="text-center">
                            <button type="submit"
                                    class="bg-mountain-700 text-white px-8 py-3 rounded-lg hover:bg-mountain-800 transition-colors focus:ring-2 focus:ring-mountain-700 focus:ring-offset-2">
                                Pridať spomienku
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Virtuálne sviečky -->
                <div class="text-center mb-12 fade-in">
                    <div class="bg-stone-100 p-8 rounded-lg shadow-lg">
                        <h3 class="text-2xl font-lora font-semibold text-mountain-700 mb-4">Zapáliť virtuálnu sviečku</h3>
                        <p class="text-stone-600 mb-6">Zapálených sviečok: <span id="candleNumber" class="font-bold text-mountain-700">127</span></p>
                        <button id="lightCandle"
                                class="bg-earth-600 text-white px-6 py-3 rounded-lg hover:bg-earth-700 transition-colors focus:ring-2 focus:ring-earth-600 focus:ring-offset-2">
                            🕯️ Zapáliť sviečku
                        </button>
                    </div>
                </div>

                <!-- Zobrazenie spomienok -->
                <div id="memoriesContainer" class="space-y-6 fade-in">
                    <!-- Spomienky sa načítajú dynamicky -->
                </div>
            </div>
        </section>

        <!-- Jemný predel -->
        <div class="section-divider"></div>

        <!-- Mapa Section -->
        <section id="mapa" class="py-24 bg-stone-100">
            <div class="max-w-6xl mx-auto px-6">
                <div class="text-center mb-16 fade-in">
                    <h2 class="text-4xl lg:text-5xl font-lora font-semibold text-mountain-700 mb-4">Martinove miesta</h2>
                    <p class="text-xl text-stone-600 font-lora italic">Miesta, ktoré miloval a kde zanechal svoj odkaz</p>
                </div>

                <!-- Mapa -->
                <div class="map-container mb-12 fade-in">
                    <div id="map" class="w-full h-96 rounded-lg"></div>
                </div>

                <!-- Významné miesta -->
                <div class="map-points fade-in">
                    <div class="map-point">
                        <div class="map-point-title">Štrbské pleso</div>
                        <p class="text-stone-600">Martinovo najobľúbenejšie miesto na fotenie úsvitov. Tu stretol svoju životnú lásku Annu a tu sa vrátil vždy, keď potreboval pokoj a inšpiráciu.</p>
                    </div>

                    <div class="map-point">
                        <div class="map-point-title">Skalnaté pleso</div>
                        <p class="text-stone-600">Miesto prvého horoledeckého úspechu. Martin tu ako mladý muž zdolal svoju prvú náročnú cestu a rozhodol sa venovať životu v horách.</p>
                    </div>

                    <div class="map-point">
                        <div class="map-point-title">Tatranská Lomnica</div>
                        <p class="text-stone-600">Východiskový bod mnohých Martinových túr. Odtiaľto vodil turistov na nezabudnuteľné výlety a tu sa oženil so svojou milovanou Annou.</p>
                    </div>

                    <div class="map-point">
                        <div class="map-point-title">Popradské Pleso</div>
                        <p class="text-stone-600">Kľudné miesto, kde Martin rád trávil čas s deťmi. Učil ich tu láske k prírode a zodpovednosti voči životnému prostrediu.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-mountain-700 text-white py-12">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <h3 class="text-2xl font-lora font-semibold mb-4">Martin Varga</h3>
            <p class="text-mountain-200 mb-6">1975 - 2023 • Navždy v našich srdciach</p>
            <p class="text-mountain-300 text-sm">
                "Hory ma naučili pokore a láska ma naučila žiť. Nech moja cesta inšpiruje ostatných k hľadaniu krásy v jednoduchosti."
            </p>
        </div>
    </footer>

    <script>
        // Globálne premenné
        let currentQuoteIndex = 0;
        let quoteInterval;
        let youtubePlayer;
        let currentTrackIndex = 0;
        let isPlaying = false;

        // Playlist s YouTube video ID - skladby pre Martina Vargu
        const playlist = [
            { id: 'Ywg2pvva-wg', title: 'Banská Bystrica', artist: 'Honza Nedved', duration: '4:20' },
            { id: 'lRca7evReSs', title: 'Tam u nebeských bran', artist: 'Michal Tučný', duration: '3:45' },
            { id: 'Epkxt9kmTjE', title: 'Rosa na kolejích', artist: 'Wabi Daněk', duration: '4:15' }
        ];

        // DOM Content Loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        // Inicializácia aplikácie
        function initializeApp() {
            setupSmoothScroll();
            setupProgressBar();
            setupNavigation();
            setupFadeInAnimations();
            setupTimelineAnimations();
            setupQuotesCarousel();
            setupMemoryBook();
            setupMapbox();
            setupPerformanceOptimizations();
            setupAccessibilityFeatures();
            setupErrorHandling();

            // Audio prehrávač inicializujeme s oneskorením
            setTimeout(() => {
                setupAudioPlayer();
            }, 1000);
        }

        // Smooth scroll pre navigáciu
        function setupSmoothScroll() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Progress bar
        function setupProgressBar() {
            const progressBar = document.getElementById('progressBar');

            function updateProgressBar() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                progressBar.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateProgressBar);
            updateProgressBar();
        }

        // Navigácia
        function setupNavigation() {
            const hamburger = document.getElementById('hamburger');
            const mobileMenu = document.getElementById('mobileMenu');
            const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');

            // Mobile menu toggle
            if (hamburger) {
                hamburger.addEventListener('click', () => {
                    mobileMenu.style.display = mobileMenu.style.display === 'flex' ? 'none' : 'flex';
                });
            }

            // Active navigation
            function updateActiveNavigation() {
                const sections = document.querySelectorAll('section[id]');
                const scrollPos = window.scrollY + 100;

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');

                    if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === `#${sectionId}`) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            }

            window.addEventListener('scroll', updateActiveNavigation);
            updateActiveNavigation();

            // Close mobile menu when link is clicked
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    if (mobileMenu) {
                        mobileMenu.style.display = 'none';
                    }
                });
            });
        }

        // Fade-in animácie
        function setupFadeInAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });
        }

        // Timeline animácie
        function setupTimelineAnimations() {
            const timelineItems = document.querySelectorAll('.timeline-item');

            const timelineObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate');
                    }
                });
            }, {
                threshold: 0.3,
                rootMargin: '50px'
            });

            timelineItems.forEach(item => {
                timelineObserver.observe(item);
            });

            // Timeline content click interactions
            const timelineContents = document.querySelectorAll('.timeline-content');
            timelineContents.forEach(content => {
                content.addEventListener('click', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                    this.style.boxShadow = '0 20px 40px rgba(74, 103, 65, 0.3)';

                    setTimeout(() => {
                        this.style.transform = '';
                        this.style.boxShadow = '';
                    }, 300);
                });

                content.addEventListener('mouseenter', function() {
                    this.style.borderLeft = '4px solid #8B7355';
                });

                content.addEventListener('mouseleave', function() {
                    this.style.borderLeft = '';
                });
            });
        }

        // Quotes carousel
        function setupQuotesCarousel() {
            const quotes = document.querySelectorAll('.quote-slide');
            const dots = document.querySelectorAll('.carousel-dot');
            const prevBtn = document.getElementById('prevQuote');
            const nextBtn = document.getElementById('nextQuote');

            function showQuote(index) {
                quotes.forEach((quote, i) => {
                    quote.classList.toggle('active', i === index);
                });
                dots.forEach((dot, i) => {
                    dot.classList.toggle('active', i === index);
                });
            }

            function nextQuote() {
                currentQuoteIndex = (currentQuoteIndex + 1) % quotes.length;
                showQuote(currentQuoteIndex);
            }

            function prevQuote() {
                currentQuoteIndex = (currentQuoteIndex - 1 + quotes.length) % quotes.length;
                showQuote(currentQuoteIndex);
            }

            // Event listeners
            if (nextBtn) nextBtn.addEventListener('click', nextQuote);
            if (prevBtn) prevBtn.addEventListener('click', prevQuote);

            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentQuoteIndex = index;
                    showQuote(currentQuoteIndex);
                });
            });

            // Auto-advance quotes
            quoteInterval = setInterval(nextQuote, 5000);

            // Pause on hover
            const carousel = document.querySelector('.quotes-carousel');
            if (carousel) {
                carousel.addEventListener('mouseenter', () => clearInterval(quoteInterval));
                carousel.addEventListener('mouseleave', () => {
                    quoteInterval = setInterval(nextQuote, 5000);
                });
            }
        }

        // Memory book s Airtable
        function setupMemoryBook() {
            const memoryForm = document.getElementById('memoryForm');
            const memoriesContainer = document.getElementById('memoriesContainer');
            const lightCandleBtn = document.getElementById('lightCandle');
            const candleNumber = document.getElementById('candleNumber');

            // Airtable konfigurácia
            const AIRTABLE_API_KEY = '**********************************************************************************';
            const AIRTABLE_BASE_ID = 'appjrq70ohFM9hrvM';
            const AIRTABLE_TABLE_ID = 'tblKbwEEyfqUXhfyh';

            let candleCount = parseInt(localStorage.getItem('candleCount') || '127');
            if (candleNumber) candleNumber.textContent = candleCount;

            // Načítanie spomienok
            async function loadMemories() {
                try {
                    const response = await fetch(`https://api.airtable.com/v0/${AIRTABLE_BASE_ID}/${AIRTABLE_TABLE_ID}?sort[0][field]=datum&sort[0][direction]=desc`, {
                        headers: {
                            'Authorization': `Bearer ${AIRTABLE_API_KEY}`
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        displayMemories(data.records);
                    } else {
                        console.warn('Nepodarilo sa načítať spomienky z Airtable');
                        displayFallbackMemories();
                    }
                } catch (error) {
                    console.error('Chyba pri načítavaní spomienok:', error);
                    displayFallbackMemories();
                }
            }

            // Zobrazenie spomienok
            function displayMemories(records) {
                if (!memoriesContainer) return;

                memoriesContainer.innerHTML = '';

                records.forEach(record => {
                    const fields = record.fields;
                    if (fields.schvalene) {
                        const memoryDiv = createMemoryElement(fields.meno, fields.spomienka, fields.datum);
                        memoriesContainer.appendChild(memoryDiv);
                    }
                });
            }

            // Fallback spomienky
            function displayFallbackMemories() {
                if (!memoriesContainer) return;

                const fallbackMemories = [
                    { meno: 'Anna Vargová', spomienka: 'Martin bol najlepší manžel a otec. Jeho láska k horám a rodine bola nekonečná. Chýba mi každý deň.', datum: '2023-12-15' },
                    { meno: 'Tomáš Varga', spomienka: 'Otec ma naučil všetko o horách a o živote. Jeho múdrosť a láska ma budú sprevádzať navždy.', datum: '2023-12-14' },
                    { meno: 'Ján Novák', spomienka: 'Martin bol výnimočný horský vodca. Vďaka nemu som si zamiloval Tatry. Bol to skutočný gentleman.', datum: '2023-12-13' }
                ];

                memoriesContainer.innerHTML = '';
                fallbackMemories.forEach(memory => {
                    const memoryDiv = createMemoryElement(memory.meno, memory.spomienka, memory.datum);
                    memoriesContainer.appendChild(memoryDiv);
                });
            }

            // Vytvorenie elementu spomienky
            function createMemoryElement(meno, spomienka, datum) {
                const memoryDiv = document.createElement('div');
                memoryDiv.className = 'bg-stone-100 p-6 rounded-lg shadow-lg';

                const formattedDate = new Date(datum).toLocaleDateString('sk-SK');

                memoryDiv.innerHTML = `
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-mountain-700 rounded-full flex items-center justify-center text-white font-semibold">
                            ${meno.charAt(0).toUpperCase()}
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between items-start mb-2">
                                <h4 class="font-semibold text-mountain-700">${meno}</h4>
                                <span class="text-sm text-stone-500">${formattedDate}</span>
                            </div>
                            <p class="text-stone-600 leading-relaxed">${spomienka}</p>
                        </div>
                    </div>
                `;

                return memoryDiv;
            }

            // Pridanie novej spomienky
            if (memoryForm) {
                memoryForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const data = {
                        fields: {
                            meno: formData.get('meno'),
                            spomienka: formData.get('spomienka'),
                            email: formData.get('email') || '',
                            datum: new Date().toISOString().split('T')[0],
                            schvalene: false
                        }
                    };

                    try {
                        const response = await fetch(`https://api.airtable.com/v0/${AIRTABLE_BASE_ID}/${AIRTABLE_TABLE_ID}`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${AIRTABLE_API_KEY}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(data)
                        });

                        if (response.ok) {
                            showMessage('Ďakujeme za vašu spomienku. Po schválení bude zobrazená.', 'success');
                            this.reset();
                        } else {
                            throw new Error('Nepodarilo sa odoslať spomienku');
                        }
                    } catch (error) {
                        console.error('Chyba pri odosielaní spomienky:', error);
                        showMessage('Spomienka bola uložená lokálne. Ďakujeme.', 'info');

                        // Lokálne uloženie ako fallback
                        const memoryDiv = createMemoryElement(data.fields.meno, data.fields.spomienka, data.fields.datum);
                        memoriesContainer.insertBefore(memoryDiv, memoriesContainer.firstChild);
                        this.reset();
                    }
                });
            }

            // Zapálenie sviečky
            if (lightCandleBtn) {
                lightCandleBtn.addEventListener('click', function() {
                    candleCount++;
                    if (candleNumber) candleNumber.textContent = candleCount;
                    localStorage.setItem('candleCount', candleCount.toString());

                    // Animácia
                    this.style.transform = 'scale(1.1)';
                    this.style.background = '#f97316';

                    setTimeout(() => {
                        this.style.transform = '';
                        this.style.background = '';
                    }, 200);

                    showMessage('✨ Sviečka zapálená', 'success');
                });
            }

            // Načítanie spomienok pri štarte
            loadMemories();
        }

        // Audio prehrávač s YouTube
        function setupAudioPlayer() {
            // Skontroluj či je YouTube API dostupné
            if (typeof YT !== 'undefined' && YT.Player) {
                initializeYouTubePlayer();
            } else {
                // Počkaj na načítanie API
                window.onYouTubeIframeAPIReady = initializeYouTubePlayer;

                // Fallback ak sa API nenačíta do 5 sekúnd
                setTimeout(() => {
                    if (typeof YT === 'undefined') {
                        setupFallbackPlayer();
                    }
                }, 5000);
            }

            function initializeYouTubePlayer() {
                try {
                    youtubePlayer = new YT.Player('youtube-player', {
                        height: '0',
                        width: '0',
                        videoId: playlist[0].id,
                        playerVars: {
                            autoplay: 0,
                            controls: 0,
                            disablekb: 1,
                            fs: 0,
                            modestbranding: 1,
                            rel: 0
                        },
                        events: {
                            onReady: onPlayerReady,
                            onStateChange: onPlayerStateChange,
                            onError: onPlayerError
                        }
                    });
                } catch (error) {
                    console.error('Chyba pri inicializácii YouTube prehrávača:', error);
                    setupFallbackPlayer();
                }
            }

            function setupFallbackPlayer() {
                const audioPlayer = document.getElementById('audioPlayer');
                if (audioPlayer) {
                    audioPlayer.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <h3 style="color: #4A6741; margin-bottom: 15px; font-family: 'Lora', serif;">🎵 Hudba Martina Vargu</h3>
                            <p style="margin-bottom: 20px;">Prehrávač hudby momentálne nie je dostupný.</p>
                            <div style="display: flex; flex-direction: column; gap: 10px; max-width: 300px; margin: 0 auto;">
                                <a href="https://www.youtube.com/watch?v=Ywg2pvva-wg" target="_blank"
                                   style="color: #4A6741; text-decoration: none; padding: 10px; border: 2px solid #4A6741; border-radius: 8px; transition: all 0.3s ease;">
                                   🎵 Banská Bystrica - Honza Nedved
                                </a>
                                <a href="https://www.youtube.com/watch?v=lRca7evReSs" target="_blank"
                                   style="color: #4A6741; text-decoration: none; padding: 10px; border: 2px solid #4A6741; border-radius: 8px; transition: all 0.3s ease;">
                                   🎵 Tam u nebeských bran - Michal Tučný
                                </a>
                                <a href="https://www.youtube.com/watch?v=Epkxt9kmTjE" target="_blank"
                                   style="color: #4A6741; text-decoration: none; padding: 10px; border: 2px solid #4A6741; border-radius: 8px; transition: all 0.3s ease;">
                                   🎵 Rosa na kolejích - Wabi Daněk
                                </a>
                            </div>
                        </div>
                    `;
                }
            }

            function onPlayerReady(event) {
                console.log('YouTube prehrávač je pripravený');
                setupPlayerControls();
                updateTrackInfo();

                // Nastav počiatočnú hlasitosť
                const volumeSlider = document.getElementById('volumeSlider');
                if (volumeSlider && youtubePlayer.setVolume) {
                    youtubePlayer.setVolume(volumeSlider.value);
                }
            }

            function onPlayerStateChange(event) {
                if (event.data === YT.PlayerState.ENDED) {
                    nextTrack();
                }

                const playPauseBtn = document.getElementById('playPauseBtn');
                if (playPauseBtn) {
                    if (event.data === YT.PlayerState.PLAYING) {
                        playPauseBtn.textContent = '⏸';
                        isPlaying = true;
                    } else {
                        playPauseBtn.textContent = '▶';
                        isPlaying = false;
                    }
                }

                // Aktualizuj čas
                if (event.data === YT.PlayerState.PLAYING) {
                    updateTimeDisplay();
                }
            }

            function onPlayerError(event) {
                const errorCode = event.data;
                let errorMessage = 'Chyba pri prehrávaní skladby';

                switch(errorCode) {
                    case 2:
                        errorMessage = 'Neplatné video ID';
                        break;
                    case 5:
                        errorMessage = 'HTML5 prehrávač nie je podporovaný';
                        break;
                    case 100:
                        errorMessage = 'Video nebolo nájdené';
                        break;
                    case 101:
                    case 150:
                        errorMessage = 'Video nie je dostupné pre vloženie';
                        break;
                    default:
                        errorMessage = `Neznáma chyba (${errorCode})`;
                }

                console.error('YouTube prehrávač error:', errorCode, errorMessage);
                showMessage(errorMessage, 'error');

                // Ak je to chyba vloženia (150), skús ďalšiu skladbu
                if (errorCode === 150 || errorCode === 101) {
                    setTimeout(() => {
                        nextTrack();
                    }, 2000);
                } else {
                    // Pre iné chyby, zobraz fallback
                    setTimeout(() => {
                        setupFallbackPlayer();
                    }, 3000);
                }
            }

            function setupPlayerControls() {
                const playPauseBtn = document.getElementById('playPauseBtn');
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');
                const volumeSlider = document.getElementById('volumeSlider');

                if (playPauseBtn) {
                    playPauseBtn.addEventListener('click', () => {
                        try {
                            if (isPlaying) {
                                youtubePlayer.pauseVideo();
                            } else {
                                youtubePlayer.playVideo();
                            }
                        } catch (error) {
                            console.error('Chyba pri ovládaní prehrávania:', error);
                            showMessage('Chyba pri ovládaní prehrávača', 'error');
                        }
                    });
                }

                if (prevBtn) prevBtn.addEventListener('click', prevTrack);
                if (nextBtn) nextBtn.addEventListener('click', nextTrack);

                if (volumeSlider) {
                    volumeSlider.addEventListener('input', (e) => {
                        try {
                            if (youtubePlayer && youtubePlayer.setVolume) {
                                youtubePlayer.setVolume(e.target.value);
                            }
                        } catch (error) {
                            console.error('Chyba pri nastavovaní hlasitosti:', error);
                        }
                    });
                }

                // Playlist items
                document.querySelectorAll('.playlist-item').forEach((item, index) => {
                    item.addEventListener('click', () => {
                        currentTrackIndex = index;
                        loadTrack();
                        updatePlaylistUI();
                    });
                });

                console.log('Audio ovládanie nastavené');
            }

            function updateTimeDisplay() {
                if (!youtubePlayer || !youtubePlayer.getCurrentTime) return;

                try {
                    const currentTime = youtubePlayer.getCurrentTime();
                    const duration = youtubePlayer.getDuration();

                    const currentTimeEl = document.getElementById('currentTime');
                    const totalTimeEl = document.getElementById('totalTime');

                    if (currentTimeEl) {
                        currentTimeEl.textContent = formatTime(currentTime);
                    }

                    if (totalTimeEl && duration) {
                        totalTimeEl.textContent = formatTime(duration);
                    }
                } catch (error) {
                    console.error('Chyba pri aktualizácii času:', error);
                }
            }

            function formatTime(seconds) {
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins}:${secs.toString().padStart(2, '0')}`;
            }

            function loadTrack() {
                try {
                    if (youtubePlayer && youtubePlayer.loadVideoById) {
                        console.log('Načítavam skladbu:', playlist[currentTrackIndex].title);
                        youtubePlayer.loadVideoById(playlist[currentTrackIndex].id);
                        updateTrackInfo();
                        updatePlaylistUI();
                    }
                } catch (error) {
                    console.error('Chyba pri načítavaní skladby:', error);
                    showMessage('Chyba pri načítavaní skladby', 'error');
                }
            }

            function updateTrackInfo() {
                const trackTitle = document.getElementById('trackTitle');
                const trackArtist = document.querySelector('.track-artist');

                if (trackTitle) {
                    trackTitle.textContent = playlist[currentTrackIndex].title;
                }

                if (trackArtist) {
                    trackArtist.textContent = playlist[currentTrackIndex].artist || 'Martin Varga';
                }
            }

            function nextTrack() {
                currentTrackIndex = (currentTrackIndex + 1) % playlist.length;
                loadTrack();
            }

            function prevTrack() {
                currentTrackIndex = (currentTrackIndex - 1 + playlist.length) % playlist.length;
                loadTrack();
            }

            function updatePlaylistUI() {
                document.querySelectorAll('.playlist-item').forEach((item, index) => {
                    item.classList.toggle('active', index === currentTrackIndex);
                });
            }

            // Interval pre aktualizáciu času
            let timeUpdateInterval;

            function startTimeUpdate() {
                if (timeUpdateInterval) clearInterval(timeUpdateInterval);
                timeUpdateInterval = setInterval(updateTimeDisplay, 1000);
            }

            function stopTimeUpdate() {
                if (timeUpdateInterval) {
                    clearInterval(timeUpdateInterval);
                    timeUpdateInterval = null;
                }
            }

            // Upravená onPlayerStateChange funkcia
            function onPlayerStateChangeUpdated(event) {
                if (event.data === YT.PlayerState.ENDED) {
                    nextTrack();
                }

                const playPauseBtn = document.getElementById('playPauseBtn');
                if (playPauseBtn) {
                    if (event.data === YT.PlayerState.PLAYING) {
                        playPauseBtn.textContent = '⏸';
                        isPlaying = true;
                        startTimeUpdate();
                    } else {
                        playPauseBtn.textContent = '▶';
                        isPlaying = false;
                        if (event.data === YT.PlayerState.PAUSED) {
                            stopTimeUpdate();
                        }
                    }
                }
            }

            // Nahradíme pôvodnú funkciu
            window.onPlayerStateChange = onPlayerStateChangeUpdated;
        }

        // Mapbox mapa
        function setupMapbox() {
            mapboxgl.accessToken = 'pk.eyJ1IjoidmxhZG8zNjEiLCJhIjoiY21jc3dkemE1MGpsdTJrczlndnIyMnY3eiJ9.UMO7y7Q4211EV3hTAbfCOA';

            try {
                const map = new mapboxgl.Map({
                    container: 'map',
                    style: 'mapbox://styles/mapbox/outdoors-v12',
                    center: [20.0892, 49.0880], // Vysoké Tatry
                    zoom: 10
                });

                // Martinove miesta
                const martinovaMiesta = [
                    {
                        coordinates: [20.0651, 49.1193],
                        title: "Štrbské pleso",
                        description: "Martinovo najobľúbenejšie miesto na fotenie úsvitov"
                    },
                    {
                        coordinates: [20.1317, 49.1794],
                        title: "Skalnaté pleso",
                        description: "Miesto prvého horoledeckého úspechu"
                    },
                    {
                        coordinates: [20.1667, 49.1667],
                        title: "Tatranská Lomnica",
                        description: "Východiskový bod mnohých Martinových túr"
                    },
                    {
                        coordinates: [20.0833, 49.0833],
                        title: "Popradské Pleso",
                        description: "Kľudné miesto, kde Martin rád trávil čas s deťmi"
                    }
                ];

                martinovaMiesta.forEach(miesto => {
                    new mapboxgl.Marker({ color: '#4A6741' })
                        .setLngLat(miesto.coordinates)
                        .setPopup(new mapboxgl.Popup().setHTML(
                            `<h3 style="color: #4A6741; margin-bottom: 8px; font-family: 'Lora', serif;">${miesto.title}</h3><p style="color: #666; line-height: 1.4;">${miesto.description}</p>`
                        ))
                        .addTo(map);
                });

                map.addControl(new mapboxgl.NavigationControl());
                map.addControl(new mapboxgl.FullscreenControl());

            } catch (error) {
                console.error('Chyba pri načítavaní mapy:', error);
                const mapContainer = document.getElementById('map');
                if (mapContainer) {
                    mapContainer.innerHTML = `
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: linear-gradient(135deg, #f7f8f7, #eef0ed); border-radius: 10px; border: 2px dashed #4A6741;">
                            <div style="text-align: center; color: #666;">
                                <h3 style="font-size: 1.5rem; margin-bottom: 10px; color: #4A6741;">🏔️ Mapa Tatier</h3>
                                <p>Interaktívna mapa momentálne nie je dostupná</p>
                            </div>
                        </div>
                    `;
                }
            }
        }

        // Výkonnostné optimalizácie
        function setupPerformanceOptimizations() {
            // Lazy loading pre obrázky
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src || img.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img[loading="lazy"]').forEach(img => {
                    imageObserver.observe(img);
                });
            }

            // Debounce pre scroll eventy
            let scrollTimeout;
            const originalScrollHandlers = [];

            window.addEventListener('scroll', () => {
                if (scrollTimeout) clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(() => {
                    originalScrollHandlers.forEach(handler => handler());
                }, 10);
            });
        }

        // Accessibility features
        function setupAccessibilityFeatures() {
            // Keyboard navigation
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    // Close any open modals or menus
                    const mobileMenu = document.getElementById('mobileMenu');
                    if (mobileMenu && mobileMenu.style.display === 'flex') {
                        mobileMenu.style.display = 'none';
                    }
                }
            });

            // Focus management
            document.querySelectorAll('.focus-visible').forEach(element => {
                element.addEventListener('focus', function() {
                    this.style.outline = '2px solid #4A6741';
                    this.style.outlineOffset = '2px';
                });

                element.addEventListener('blur', function() {
                    this.style.outline = '';
                    this.style.outlineOffset = '';
                });
            });
        }

        // Error handling
        function setupErrorHandling() {
            window.addEventListener('error', (e) => {
                console.error('JavaScript error:', e.error);
            });

            // Image error handling
            document.addEventListener('error', (e) => {
                if (e.target.tagName === 'IMG') {
                    e.target.style.display = 'none';
                    console.warn('Obrázok sa nepodarilo načítať:', e.target.src);
                }
            }, true);
        }

        // Utility funkcie
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: ${type === 'success' ? '#4A6741' : type === 'error' ? '#dc2626' : '#6b7280'};
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                z-index: 1000;
                font-size: 16px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            `;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // Hero particles effect
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;

            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 4 + 2 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Parallax effect pre hero
        function setupParallax() {
            const heroParallax = document.querySelector('.hero-parallax');
            if (!heroParallax) return;

            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;
                heroParallax.style.transform = `translateY(${rate}px)`;
            });
        }

        // Inicializácia particles a parallax
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
            setupParallax();
        });
    </script>
</body>
</html>
